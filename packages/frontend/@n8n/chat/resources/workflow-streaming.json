{"nodes": [{"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [400, 224], "id": "9593988a-2ca5-4a82-bd3a-3d8fcb69036d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "Rr1g6PqGGpNJcaBf", "name": "OpenAi account"}}}, {"parameters": {"options": {"returnIntermediateSteps": false, "enableStreaming": false}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [400, 48], "id": "72b37ab2-7352-476b-bca6-5b41e2290082", "name": "AI Agent"}, {"parameters": {"public": true, "options": {"responseMode": "streaming"}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.2, "position": [32, 48], "id": "012ecda4-3ae8-4328-a371-c7ae63cabf1c", "name": "When chat message received", "webhookId": "022d6461-e68d-4531-b713-833953c388c2"}], "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "1d6725ae695aac02e442b627cd2266d305eba55a427b2dcc7702a0271b70043c"}}