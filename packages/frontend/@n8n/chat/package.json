{"name": "@n8n/chat", "version": "0.51.0", "scripts": {"dev": "pnpm run storybook", "build": "pnpm build:vite && pnpm build:bundle", "build:vite": "cross-env vite build", "build:bundle": "cross-env INCLUDE_VUE=true vite build", "preview": "vite preview", "test:dev": "vitest", "test": "vitest run", "typecheck": "vue-tsc --noEmit", "lint": "eslint src --quiet", "lint:fix": "eslint src --fix", "lint:styles": "stylelint \"src/**/*.{scss,sass,vue}\" --cache", "lint:styles:fix": "stylelint \"src/**/*.{scss,sass,vue}\" --fix --cache", "format": "biome format --write src .storybook && prettier --write src/ --ignore-path ../../../../.prettierignore", "format:check": "biome ci src .storybook && prettier --check src/ --ignore-path ../../../../.prettierignore", "storybook": "storybook dev -p 6006 --no-open", "build:storybook": "storybook build"}, "types": "./dist/index.d.ts", "main": "./dist/chat.umd.js", "module": "./dist/chat.es.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/chat.es.js", "require": "./dist/chat.umd.js"}, "./style.css": {"import": "./dist/style.css", "require": "./dist/style.css"}, "./*": {"import": "./*", "require": "./*"}}, "dependencies": {"@n8n/design-system": "workspace:*", "@vueuse/core": "catalog:frontend", "highlight.js": "catalog:frontend", "markdown-it-link-attributes": "^4.0.1", "uuid": "catalog:", "vue": "catalog:frontend", "vue-markdown-render": "catalog:frontend"}, "devDependencies": {"@iconify-json/mdi": "^1.1.54", "@n8n/storybook": "workspace:*", "@n8n/eslint-config": "workspace:*", "@n8n/stylelint-config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@vitejs/plugin-vue": "catalog:frontend", "@vitest/coverage-v8": "catalog:", "unplugin-icons": "^0.19.0", "vite": "catalog:", "vitest": "catalog:", "vite-plugin-dts": "^4.5.3", "vue-tsc": "catalog:frontend"}, "files": ["README.md", "dist"]}