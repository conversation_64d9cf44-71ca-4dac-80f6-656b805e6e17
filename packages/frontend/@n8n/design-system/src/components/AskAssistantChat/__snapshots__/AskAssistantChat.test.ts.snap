// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AskAssistantChat > does not render retry button if no error is present 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <div
                  class="assistantText rendered-content"
                >
                  <p>
                    Hi Max! Here is my top solution to fix the error in your 
                    <strong>
                      Transform data
                    </strong>
                     node👇
                  </p>
                  

                </div>
                <!--v-if-->
                <!--v-if-->
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas"
        data-test-id="chat-input"
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders chat with messages correctly 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <div
                  class="assistantText rendered-content"
                >
                  <p>
                    Hi Max! Here is my top solution to fix the error in your 
                    <strong>
                      Transform data
                    </strong>
                     node👇
                  </p>
                  

                </div>
                <!--v-if-->
                <!--v-if-->
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <!--v-if-->
              
              <div
                class="container"
                data-test-id="code-diff-suggestion"
              >
                <div
                  class="title"
                >
                  Short solution description here that can spill over to two lines
                </div>
                <div
                  class="diffSection"
                >
                  
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="del diffContent"
                    >
                      <span>
                         - 
                      </span>
                      <span>
                        The Way that can be told of is not the eternal Way;
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="del diffContent"
                    >
                      <span>
                         - 
                      </span>
                      <span>
                        The name that can be named is not the eternal name.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="del diffContent"
                    >
                      <span>
                         - 
                      </span>
                      <span>
                        The Named is the mother of all things.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       1
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span>
                        The named is the mother of all things.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       2
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span />
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       3
                    </div>
                    <div
                      class="normal diffContent"
                    >
                      <span>
                           
                      </span>
                      <span>
                         The two are the same,
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       4
                    </div>
                    <div
                      class="normal diffContent"
                    >
                      <span>
                           
                      </span>
                      <span>
                         But after they are produced,
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       5
                    </div>
                    <div
                      class="normal diffContent"
                    >
                      <span>
                           
                      </span>
                      <span>
                         they have different names.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       6
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span>
                        They both may be called deep and profound.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       7
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span>
                        Deeper and more profound,
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       8
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span>
                        The door of all subtleties!
                      </span>
                    </div>
                  </div>
                  
                </div>
                <div
                  class="actions"
                >
                  <n8n-button-stub
                    active="false"
                    block="false"
                    data-test-id="replace-code-button"
                    disabled="false"
                    element="button"
                    icon="refresh-cw"
                    label=""
                    loading="false"
                    outline="false"
                    size="mini"
                    square="false"
                    text="false"
                    type="primary"
                  />
                </div>
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          <data
            data-test-id="chat-message-user"
          >
            <div
              class="message"
            >
              <div
                class="roleName"
              >
                
                <n8n-avatar-stub
                  colors="--color-primary,--color-secondary,--color-avatar-accent-1,--color-avatar-accent-2,--color-primary-tint-1"
                  firstname="Kobi"
                  lastname="Dog"
                  size="xsmall"
                />
                <span>
                  You
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <span
                  class="rendered-content"
                >
                  <p>
                    Give it to me 
                    <strong>
                      ignore this markdown
                    </strong>
                  </p>
                  

                </span>
                <!--v-if-->
                <!--v-if-->
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="block"
              >
                <div
                  class="blockTitle"
                >
                  Credential doesn’t have correct permissions to send a message 
                  <!--v-if-->
                </div>
                <div
                  class="blockBody"
                >
                  <span
                    class="rendered-content"
                  >
                    <p>
                      Solution steps:
                    </p>
                    

                    <ol>
                      

                      <li>
                        Lorem ipsum dolor sit amet, consectetur 
                        <strong>
                          adipiscing
                        </strong>
                         elit. Proin id nulla placerat, tristique ex at, euismod dui.
                      </li>
                      

                      <li>
                        Copy this into somewhere
                      </li>
                      

                      <li>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin id nulla placerat, tristique ex at, euismod dui.
                      </li>
                      

                      <li>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin id nulla placerat, tristique ex at, euismod dui.
                        <br />
                        
Testing more code
                      </li>
                      

                    </ol>
                    

                    <ul>
                      

                      <li>
                        Unordered item 1
                      </li>
                      

                      <li>
                        Unordered item 2
                      </li>
                      

                    </ul>
                    

                  </span>
                  <!--v-if-->
                </div>
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <!--v-if-->
              
              <div
                class="container"
                data-test-id="code-diff-suggestion"
              >
                <div
                  class="title"
                >
                  Short solution with min height
                </div>
                <div
                  class="diffSection"
                >
                  
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="del diffContent"
                    >
                      <span>
                         - 
                      </span>
                      <span>
                        The Way that can be told of is not the eternal Way;
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="del diffContent"
                    >
                      <span>
                         - 
                      </span>
                      <span>
                        The name that can be named is not the eternal name.
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       1
                    </div>
                    <div
                      class="add diffContent"
                    >
                      <span>
                         + 
                      </span>
                      <span>
                        The door of all subtleties!
                      </span>
                    </div>
                  </div>
                  <div
                    class="diff"
                  >
                    <div
                      class="lineNumber"
                    >
                      <!-- ln1 is line number in original text -->
                      <!-- ln2 is line number in updated text -->
                       
                    </div>
                    <div
                      class="filler diffContent"
                    >
                      <span>
                           
                      </span>
                      <span />
                    </div>
                  </div>
                  
                </div>
                <div
                  class="actions"
                >
                  <n8n-button-stub
                    active="false"
                    block="false"
                    data-test-id="replace-code-button"
                    disabled="false"
                    element="button"
                    icon="refresh-cw"
                    label=""
                    loading="false"
                    outline="false"
                    size="mini"
                    square="false"
                    text="false"
                    type="primary"
                  />
                </div>
              </div>
              
              <!--v-if-->
            </div>
            <div
              class="quickReplies"
            >
              <div
                class="quickRepliesTitle"
              >
                Quick reply 👇
              </div>
              
              <div
                data-test-id="quick-replies"
              >
                <n8n-button-stub
                  active="false"
                  block="false"
                  disabled="false"
                  element="button"
                  label=""
                  loading="false"
                  outline="false"
                  size="mini"
                  square="false"
                  text="false"
                  type="secondary"
                />
              </div>
              <div
                data-test-id="quick-replies"
              >
                <n8n-button-stub
                  active="false"
                  block="false"
                  disabled="false"
                  element="button"
                  label=""
                  loading="false"
                  outline="false"
                  size="mini"
                  square="false"
                  text="false"
                  type="secondary"
                />
              </div>
              
            </div>
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas"
        data-test-id="chat-input"
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders default placeholder chat correctly 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="placeholder"
        data-test-id="placeholder-message"
      >
        
        <div
          class="greeting"
        >
          Hi Kobi 👋
        </div>
        <div
          class="info"
        >
          <p>
            I can answer most questions about building workflows in n8n.
          </p>
          <p>
            For specific tasks, you’ll see the 
            <button
              class="button"
              data-test-id="ask-assistant-button"
              style="height: 18px;"
              tabindex="-1"
            >
              <div>
                <div
                  style="padding: 0px 6px;"
                >
                  <svg
                    class="icon"
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="url(#paint0_linear_173_12825)"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                  <span
                    class="text small"
                  >
                    Ask Assistant
                  </span>
                </div>
              </div>
            </button>
             button in the UI.
          </p>
          <p>
            How can I help?
          </p>
        </div>
        
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas"
        data-test-id="chat-input"
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders end of session chat correctly 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <div
                  class="assistantText rendered-content"
                >
                  <p>
                    Hi Max! Here is my top solution to fix the error in your 
                    <strong>
                      Transform data
                    </strong>
                     node👇
                  </p>
                  

                </div>
                <!--v-if-->
                <!--v-if-->
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
              is-last-message="true"
              streaming="false"
            >
              <!--v-if-->
              
              <div
                class="eventText"
                data-test-id="chat-message-system"
              >
                <span>
                  This Assistant session has ended. To start a new session with the Assistant, click an
                </span>
                <button
                  class="button"
                  data-test-id="ask-assistant-button"
                  style="height: 18px;"
                  tabindex="-1"
                >
                  <div>
                    <div
                      style="padding: 0px 6px;"
                    >
                      <svg
                        class="icon"
                        fill="none"
                        height="10"
                        viewBox="0 0 24 24"
                        width="10"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                          fill="url(#paint0_linear_173_12825)"
                        />
                        <defs>
                          <lineargradient
                            gradientUnits="userSpaceOnUse"
                            id="paint0_linear_173_12825"
                            x1="-3.67094e-07"
                            x2="28.8315"
                            y1="-0.000120994"
                            y2="9.82667"
                          >
                            <stop
                              stop-color="var(--color-assistant-highlight-1)"
                            />
                            <stop
                              offset="0.495"
                              stop-color="var(--color-assistant-highlight-2)"
                            />
                            <stop
                              offset="1"
                              stop-color="var(--color-assistant-highlight-3)"
                            />
                          </lineargradient>
                        </defs>
                      </svg>
                      <span
                        class="text small"
                      >
                        Ask Assistant
                      </span>
                    </div>
                  </div>
                </button>
                <span>
                  button in n8n
                </span>
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper disabledInput"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas disabled"
        data-test-id="chat-input"
        disabled=""
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders error message correctly with retry button 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
              is-last-message="true"
              streaming="false"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="error"
                data-test-id="chat-message-system"
              >
                <p
                  class="errorText"
                >
                  <n8n-icon-stub
                    class="errorIcon"
                    icon="triangle-alert"
                    size="small"
                    spin="false"
                  />
                   This is an error message.
                </p>
                <n8n-button-stub
                  active="false"
                  block="false"
                  class="retryButton"
                  data-test-id="error-retry-button"
                  disabled="false"
                  element="button"
                  label=""
                  loading="false"
                  outline="false"
                  size="mini"
                  square="false"
                  text="false"
                  type="secondary"
                />
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas"
        data-test-id="chat-input"
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders message with code snippet 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <div
                  class="assistantText rendered-content"
                >
                  <p>
                    Hi Max! Here is my top solution to fix the error in your 
                    <strong>
                      Transform data
                    </strong>
                     node👇
                  </p>
                  

                </div>
                <div
                  class="code-snippet"
                  data-test-id="assistant-code-snippet"
                >
                  <!--v-if-->
                  <div
                    class="snippet-content rendered-content"
                    data-test-id="assistant-code-snippet-content"
                  >
                    <p>
                      node.on('input', function(msg) {
                      <br />
                      
if (msg.seed) { dummyjson.seed = msg.seed; }
                      <br />
                      
try {
                      <br />
                      
var value = dummyjson.parse(node.template, {mockdata: msg});
                      <br />
                      
if (node.syntax === 'json') {
                      <br />
                      
try { value = JSON.parse(value); }
                      <br />
                      
catch(e) { node.error(RED._('datagen.errors.json-error')); }
                      <br />
                      
}
                      <br />
                      
if (node.fieldType === 'msg') {
                      <br />
                      
RED.util.setMessageProperty(msg,node.field,value);
                      <br />
                      
}
                      <br />
                      
else if (node.fieldType === 'flow') {
                      <br />
                      
node.context().flow.set(node.field,value);
                      <br />
                      
}
                      <br />
                      
else if (node.fieldType === 'global') {
                      <br />
                      
node.context().global.set(node.field,value);
                      <br />
                      
}
                      <br />
                      
node.send(msg);
                      <br />
                      
}
                      <br />
                      
catch(e) {
                    </p>
                  </div>
                </div>
                <!--v-if-->
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas"
        data-test-id="chat-input"
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;

exports[`AskAssistantChat > renders streaming chat correctly 1`] = `
<div>
  <div
    class="container"
  >
    <div
      class="header"
    >
      <div
        class="chatTitle"
      >
        <div
          class="headerText"
        >
          <svg
            fill="none"
            height="18"
            viewBox="0 0 24 24"
            width="18"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
              fill="url(#paint0_linear_173_12825)"
            />
            <defs>
              <lineargradient
                gradientUnits="userSpaceOnUse"
                id="paint0_linear_173_12825"
                x1="-3.67094e-07"
                x2="28.8315"
                y1="-0.000120994"
                y2="9.82667"
              >
                <stop
                  stop-color="var(--color-assistant-highlight-1)"
                />
                <stop
                  offset="0.495"
                  stop-color="var(--color-assistant-highlight-2)"
                />
                <stop
                  offset="1"
                  stop-color="var(--color-assistant-highlight-3)"
                />
              </lineargradient>
            </defs>
          </svg>
          <span
            class="text large"
          >
            AI Assistant
          </span>
        </div>
        
        
      </div>
      <div
        class="back"
        data-test-id="close-chat-button"
      >
        <n8n-icon-stub
          color="text-base"
          icon="arrow-right"
          spin="false"
        />
      </div>
    </div>
    <div
      class="body"
    >
      <div
        class="messages"
      >
        <div>
          
          <data
            data-test-id="chat-message-assistant"
          >
            <div
              class="message"
            >
              <div
                class="roleName userSection"
              >
                
                <div
                  class="container small"
                >
                  <svg
                    fill="none"
                    height="10"
                    viewBox="0 0 24 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M19.9658 14.0171C19.9679 14.3549 19.8654 14.6851 19.6722 14.9622C19.479 15.2393 19.2046 15.4497 18.8869 15.5645L13.5109 17.5451L11.5303 22.9211C11.4137 23.2376 11.2028 23.5107 10.9261 23.7037C10.6494 23.8966 10.3202 24 9.9829 24C9.64559 24 9.3164 23.8966 9.0397 23.7037C8.76301 23.5107 8.55212 23.2376 8.43549 22.9211L6.45487 17.5451L1.07888 15.5645C0.762384 15.4479 0.489262 15.237 0.296347 14.9603C0.103431 14.6836 0 14.3544 0 14.0171C0 13.6798 0.103431 13.3506 0.296347 13.0739C0.489262 12.7972 0.762384 12.5863 1.07888 12.4697L6.45487 10.4891L8.43549 5.11309C8.55212 4.79659 8.76301 4.52347 9.0397 4.33055C9.3164 4.13764 9.64559 4.0342 9.9829 4.0342C10.3202 4.0342 10.6494 4.13764 10.9261 4.33055C11.2028 4.52347 11.4137 4.79659 11.5303 5.11309L13.5109 10.4891L18.8869 12.4697C19.2046 12.5845 19.479 12.7949 19.6722 13.072C19.8654 13.3491 19.9679 13.6793 19.9658 14.0171ZM14.1056 4.12268H15.7546V5.77175C15.7546 5.99043 15.8415 6.20015 15.9961 6.35478C16.1508 6.50941 16.3605 6.59628 16.5792 6.59628C16.7979 6.59628 17.0076 6.50941 17.1622 6.35478C17.3168 6.20015 17.4037 5.99043 17.4037 5.77175V4.12268H19.0528C19.2715 4.12268 19.4812 4.03581 19.6358 3.88118C19.7905 3.72655 19.8773 3.51682 19.8773 3.29814C19.8773 3.07946 19.7905 2.86974 19.6358 2.71511C19.4812 2.56048 19.2715 2.47361 19.0528 2.47361H17.4037V0.824535C17.4037 0.605855 17.3168 0.396131 17.1622 0.241501C17.0076 0.0868704 16.7979 0 16.5792 0C16.3605 0 16.1508 0.0868704 15.9961 0.241501C15.8415 0.396131 15.7546 0.605855 15.7546 0.824535V2.47361H14.1056C13.8869 2.47361 13.6772 2.56048 13.5225 2.71511C13.3679 2.86974 13.281 3.07946 13.281 3.29814C13.281 3.51682 13.3679 3.72655 13.5225 3.88118C13.6772 4.03581 13.8869 4.12268 14.1056 4.12268ZM23.1755 7.42082H22.3509V6.59628C22.3509 6.3776 22.2641 6.16788 22.1094 6.01325C21.9548 5.85862 21.7451 5.77175 21.5264 5.77175C21.3077 5.77175 21.098 5.85862 20.9434 6.01325C20.7887 6.16788 20.7019 6.3776 20.7019 6.59628V7.42082H19.8773C19.6586 7.42082 19.4489 7.50769 19.2943 7.66232C19.1397 7.81695 19.0528 8.02667 19.0528 8.24535C19.0528 8.46404 19.1397 8.67376 19.2943 8.82839C19.4489 8.98302 19.6586 9.06989 19.8773 9.06989H20.7019V9.89443C20.7019 10.1131 20.7887 10.3228 20.9434 10.4775C21.098 10.6321 21.3077 10.719 21.5264 10.719C21.7451 10.719 21.9548 10.6321 22.1094 10.4775C22.2641 10.3228 22.3509 10.1131 22.3509 9.89443V9.06989H23.1755C23.3941 9.06989 23.6039 8.98302 23.7585 8.82839C23.9131 8.67376 24 8.46404 24 8.24535C24 8.02667 23.9131 7.81695 23.7585 7.66232C23.6039 7.50769 23.3941 7.42082 23.1755 7.42082Z"
                      fill="white"
                    />
                    <defs>
                      <lineargradient
                        gradientUnits="userSpaceOnUse"
                        id="paint0_linear_173_12825"
                        x1="-3.67094e-07"
                        x2="28.8315"
                        y1="-0.000120994"
                        y2="9.82667"
                      >
                        <stop
                          stop-color="var(--color-assistant-highlight-1)"
                        />
                        <stop
                          offset="0.495"
                          stop-color="var(--color-assistant-highlight-2)"
                        />
                        <stop
                          offset="1"
                          stop-color="var(--color-assistant-highlight-3)"
                        />
                      </lineargradient>
                    </defs>
                  </svg>
                </div>
                <span>
                  Assistant
                </span>
                
              </div>
              
              <div
                class="textMessage"
              >
                <div
                  class="assistantText rendered-content"
                >
                  <p>
                    Hi Max! Here is my top solution to fix the error in your 
                    <strong>
                      Transform data
                    </strong>
                     node👇
                  </p>
                  

                </div>
                <!--v-if-->
                <span
                  class="blinking-cursor"
                />
              </div>
              
              <!--v-if-->
            </div>
            <!--v-if-->
          </data>
          
        </div>
        <!--v-if-->
      </div>
    </div>
    <div
      class="inputWrapper"
      data-test-id="chat-input-wrapper"
    >
      
      <textarea
        class="ignore-key-press-node-creator ignore-key-press-canvas disabled"
        data-test-id="chat-input"
        disabled=""
        placeholder="Enter your response..."
        rows="1"
        wrap="hard"
      />
      <n8n-button-stub
        active="false"
        block="false"
        class="sendButton"
        data-test-id="send-message-button"
        disabled="true"
        element="button"
        icon="send"
        label=""
        loading="false"
        outline="false"
        size="large"
        square="true"
        text="true"
        type="primary"
      />
      
    </div>
  </div>
</div>
`;
