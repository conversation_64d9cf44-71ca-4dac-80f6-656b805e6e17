@use 'mixins/mixins';
@use 'mixins/button';
@use './common/var';

@include mixins.b(color-predefine) {
	display: flex;
	font-size: 12px;
	margin-top: 8px;
	width: 280px;

	@include mixins.e(colors) {
		display: flex;
		flex: 1;
		flex-wrap: wrap;
	}

	@include mixins.e(color-selector) {
		margin: 0 0 8px 8px;
		width: 20px;
		height: 20px;
		border-radius: 4px;
		cursor: pointer;

		&:nth-child(10n + 1) {
			margin-left: 0;
		}

		&.selected {
			box-shadow: 0 0 3px 2px var(--color-primary);
		}

		> div {
			display: flex;
			height: 100%;
			border-radius: 3px;
		}

		@include mixins.when(alpha) {
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
		}
	}
}

@include mixins.b(color-hue-slider) {
	position: relative;
	box-sizing: border-box;
	width: 280px;
	height: 12px;
	background-color: #f00;
	padding: 0 2px;

	@include mixins.e(bar) {
		position: relative;
		background: linear-gradient(
			to right,
			#f00 0%,
			#ff0 17%,
			#0f0 33%,
			#0ff 50%,
			#00f 67%,
			#f0f 83%,
			#f00 100%
		);
		height: 100%;
	}

	@include mixins.e(thumb) {
		position: absolute;
		cursor: pointer;
		box-sizing: border-box;
		left: 0;
		top: 0;
		width: 4px;
		height: 100%;
		border-radius: 1px;
		background: #fff;
		border: 1px solid #f0f0f0;
		box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
		z-index: 1;
	}

	@include mixins.when(vertical) {
		width: 12px;
		height: 180px;
		padding: 2px 0;

		.el-color-hue-slider__bar {
			background: linear-gradient(
				to bottom,
				#f00 0%,
				#ff0 17%,
				#0f0 33%,
				#0ff 50%,
				#00f 67%,
				#f0f 83%,
				#f00 100%
			);
		}

		.el-color-hue-slider__thumb {
			left: 0;
			top: 0;
			width: 100%;
			height: 4px;
		}
	}
}

@include mixins.b(color-svpanel) {
	position: relative;
	width: 280px;
	height: 180px;

	@include mixins.e(('white', 'black')) {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	@include mixins.e('white') {
		background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
	}

	@include mixins.e('black') {
		background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));
	}

	@include mixins.e(cursor) {
		position: absolute;

		> div {
			cursor: head;
			width: 4px;
			height: 4px;
			box-shadow:
				0 0 0 1.5px #fff,
				inset 0 0 1px 1px rgba(0, 0, 0, 0.3),
				0 0 1px 2px rgba(0, 0, 0, 0.4);
			border-radius: 50%;
			transform: translate(-2px, -2px);
		}
	}
}

@include mixins.b(color-alpha-slider) {
	position: relative;
	box-sizing: border-box;
	width: 280px;
	height: 12px;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);

	@include mixins.e(bar) {
		position: relative;
		background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
		height: 100%;
	}

	@include mixins.e(thumb) {
		position: absolute;
		cursor: pointer;
		box-sizing: border-box;
		left: 0;
		top: 0;
		width: 4px;
		height: 100%;
		border-radius: 1px;
		background: #fff;
		border: 1px solid #f0f0f0;
		box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
		z-index: 1;
	}

	@include mixins.when(vertical) {
		width: 20px;
		height: 180px;

		.el-color-alpha-slider__bar {
			background: linear-gradient(
				to bottom,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 1) 100%
			);
		}

		.el-color-alpha-slider__thumb {
			left: 0;
			top: 0;
			width: 100%;
			height: 4px;
		}
	}
}

@include mixins.b(color-dropdown) {
	width: 300px;

	@include mixins.e(main-wrapper) {
		margin-bottom: 6px;
		display: flex;
		flex-direction: row-reverse;
		justify-content: space-between;
	}

	@include mixins.e(btns) {
		margin-top: 6px;
		text-align: right;
	}

	@include mixins.e(value) {
		float: left;
		line-height: 26px;
		font-size: 12px;
		color: var.$color-black;
		width: 160px;
	}

	@include mixins.e(btn) {
		@include button.button-small();
		@include button.button-just-primary();
	}

	@include mixins.e(link-btn) {
		@include button.button-small();

		& {
			margin-right: var(--spacing-2xs);
		}
	}
}

@include mixins.b(color-picker) {
	display: inline-block;
	position: relative;
	line-height: normal;
	height: 40px;

	@include mixins.when(disabled) {
		.el-color-picker__trigger {
			cursor: not-allowed;
		}
	}

	@include mixins.m(medium) {
		height: 36px;

		.el-color-picker__trigger {
			height: 36px;
			width: 36px;
		}

		.el-color-picker__mask {
			height: 34px;
			width: 34px;
		}
	}

	@include mixins.m(small) {
		height: 32px;

		.el-color-picker__trigger {
			height: 32px;
			width: 32px;
		}

		.el-color-picker__mask {
			height: 30px;
			width: 30px;
		}

		.el-color-picker__icon,
		.el-color-picker__empty {
			transform: translate3d(-50%, -50%, 0) scale(0.8);
		}
	}

	@include mixins.m(mini) {
		height: 28px;

		.el-color-picker__trigger {
			height: 28px;
			width: 28px;
		}

		.el-color-picker__mask {
			height: 26px;
			width: 26px;
		}

		.el-color-picker__icon,
		.el-color-picker__empty {
			transform: translate3d(-50%, -50%, 0) scale(0.8);
		}
	}

	@include mixins.e(mask) {
		height: 38px;
		width: 38px;
		border-radius: 4px;
		position: absolute;
		top: 1px;
		left: 1px;
		z-index: 1;
		cursor: not-allowed;
		background-color: rgba(255, 255, 255, 0.7);
	}

	@include mixins.e(trigger) {
		display: inline-block;
		box-sizing: border-box;
		height: 40px;
		width: 40px;
		padding: 4px;
		border: 1px solid #e6e6e6;
		border-radius: 4px;
		font-size: 0;
		position: relative;
		cursor: pointer;
	}

	@include mixins.e(color) {
		position: relative;
		display: block;
		box-sizing: border-box;
		border: 1px solid #999;
		border-radius: var(--border-radius-small);
		width: 100%;
		height: 100%;
		text-align: center;

		@include mixins.when(alpha) {
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
		}
	}

	@include mixins.e(color-inner) {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
	}

	@include mixins.e(empty) {
		font-size: 12px;
		color: #999;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate3d(-50%, -50%, 0);
	}

	@include mixins.e(icon) {
		display: inline-block;
		position: absolute;
		width: 100%;
		top: 50%;
		left: 50%;
		transform: translate3d(-50%, -50%, 0);
		color: var.$color-white;
		text-align: center;
		font-size: 12px;
	}

	@include mixins.e(panel) {
		position: absolute;
		z-index: 10;
		padding: 6px;
		box-sizing: content-box;
		background-color: var.$color-white;
		border: 1px solid var(--border-color-light);
		border-radius: var(--border-radius-base);
		box-shadow: var.$dropdown-menu-box-shadow;
	}
}
