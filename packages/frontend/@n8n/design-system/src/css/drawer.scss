@use 'mixins/mixins';
@use './common/var';

$directions: rtl, ltr, ttb, btt;

@include mixins.b(drawer) {
	position: absolute;
	box-sizing: border-box;
	background-color: var.$dialog-background-color;
	display: flex;
	flex-direction: column;
	box-shadow: 0 6px 16px rgb(68 28 23 / 6%);
	overflow: hidden;

	transition: all 0.3s;

	@each $direction in $directions {
		.#{$direction} {
			transform: translate(0, 0);
		}
	}

	&__sr-focus:focus {
		outline: none !important;
	}

	&__header {
		align-items: center;
		color: rgb(114, 118, 123);
		display: flex;
		margin-bottom: 32px;
		padding: var.$dialog-padding-primary;
		padding-bottom: 0;
		& > :first-child {
			flex: 1;
		}
	}

	&__title {
		margin: 0;
		flex: 1;
		line-height: inherit;
		font-size: 1rem;
	}

	@include mixins.e(footer) {
		padding: var.$dialog-padding-primary;
		padding-top: 10px;
		text-align: right;
	}

	&__close-btn {
		display: inline-flex;
		border: none;
		cursor: pointer;
		font-size: getCssVar('font-size-extra-large');
		color: inherit;
		background-color: transparent;
		outline: none;
		&:focus,
		&:hover {
			i {
				color: getCssVar('color-primary');
			}
		}
	}

	&__body {
		flex: 1;
		padding: 0;
		overflow: auto;
		& > * {
			box-sizing: border-box;
		}
	}

	&.ltr,
	&.rtl {
		height: 100%;
		top: 0;
		bottom: 0;
	}

	&.ttb,
	&.btt {
		width: 100%;
		left: 0;
		right: 0;
	}

	&.ltr {
		left: 0;
	}

	&.rtl {
		right: 0;
	}

	&.ttb {
		top: 0;
	}

	&.btt {
		bottom: 0;
	}
}

.el-drawer-fade {
	&-enter-active,
	&-leave-active {
		transition: all 0.3s;
	}

	&-enter-from,
	&-enter-active,
	&-enter-to,
	&-leave-from,
	&-leave-active,
	&-leave-to {
		overflow: hidden !important;
	}

	&-enter-from,
	&-leave-to {
		opacity: 0;

		@each $direction in $directions {
			.#{$direction} {
				@if $direction == ltr {
					transform: translateX(-100%);
				}

				@if $direction == rtl {
					transform: translateX(100%);
				}

				@if $direction == ttb {
					transform: translateY(-100%);
				}

				@if $direction == btt {
					transform: translateY(100%);
				}
			}
		}
	}

	&-enter-to,
	&-leave-from {
		opacity: 1;
	}
}
