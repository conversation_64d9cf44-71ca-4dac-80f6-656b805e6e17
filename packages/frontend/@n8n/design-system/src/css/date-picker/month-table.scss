@use '../common/var';
@use '../mixins/mixins';

@include mixins.b(month-table) {
	font-size: 12px;
	margin: -1px;
	border-collapse: collapse;

	td {
		text-align: center;
		padding: 8px 0;
		cursor: pointer;
		& div {
			height: 48px;
			padding: 6px 0;
			box-sizing: border-box;
		}
		&.today {
			.cell {
				color: var(--color-primary);
				font-weight: var(--font-weight-bold);
			}
			&.start-date .cell,
			&.end-date .cell {
				color: var.$color-white;
			}
		}

		&.disabled .cell {
			background-color: var.$background-color-base;
			cursor: not-allowed;
			color: var(--color-text-lighter);

			&:hover {
				color: var(--color-text-lighter);
			}
		}

		.cell {
			width: 60px;
			height: 36px;
			display: block;
			line-height: 36px;
			color: var.$datepicker-font-color;
			margin: 0 auto;
			border-radius: 18px;
			&:hover {
				color: var.$datepicker-hover-font-color;
			}
		}

		&.in-range div {
			background-color: var.$datepicker-inrange-background-color;
			&:hover {
				background-color: var.$datepicker-inrange-hover-background-color;
			}
		}
		&.start-date div,
		&.end-date div {
			color: var.$color-white;
		}

		&.start-date .cell,
		&.end-date .cell {
			color: var.$color-white;
			background-color: var.$datepicker-active-color;
		}

		&.start-date div {
			border-top-left-radius: 24px;
			border-bottom-left-radius: 24px;
		}

		&.end-date div {
			border-top-right-radius: 24px;
			border-bottom-right-radius: 24px;
		}

		&.current:not(.disabled) .cell {
			color: var.$datepicker-active-color;
		}
	}
}
