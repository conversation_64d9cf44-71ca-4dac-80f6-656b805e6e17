@use '../common/var';
@use 'sass:map';
@use 'sass:string';

@mixin breakpoint($name) {
	@if map.has-key(var.$breakpoints-spec, $name) {
		$query: map.get(var.$breakpoints-spec, $name);
		$media-query: '';

		@each $key, $value in $query {
			$media-query: '#{$media-query} and (#{$key}: #{$value})';
		}

		$media-query: string.unquote(string.slice($media-query, 6)); // Remove the initial ' and '

		@media screen and #{$media-query} {
			@content;
		}
	} @else {
		@error "No breakpoint named `#{$name}` found in `$breakpoints-spec`.";
	}
}
