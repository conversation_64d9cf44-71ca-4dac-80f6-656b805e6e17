@use 'mixins/mixins';
@use 'mixins/utils';
@use './common/var';
@use 'input';
@use 'checkbox';

@include mixins.b(transfer) {
	font-size: var.$font-size-base;

	@include mixins.e(buttons) {
		display: inline-block;
		vertical-align: middle;
		padding: 0 30px;
	}

	.button {
		display: block;
		margin: 0 auto;
		padding: 10px;
		border-radius: 50%;
		color: var.$color-white;
		background-color: var(--color-primary);
		font-size: 0;

		@include mixins.when(with-texts) {
			border-radius: var(--border-radius-base);
		}

		@include mixins.when(disabled) {
			border: var(--border-base);
			background-color: var.$background-color-base;
			color: var(--color-text-lighter);

			&:hover {
				border: var(--border-base);
				background-color: var.$background-color-base;
				color: var(--color-text-lighter);
			}
		}

		&:first-child {
			margin-bottom: 10px;
		}

		&:nth-child(2) {
			margin: 0;
		}

		i,
		span {
			font-size: 14px;
		}

		& [class*='el-icon-'] + span {
			margin-left: 0;
		}
	}
}

@include mixins.b(transfer-panel) {
	border: 1px solid var.$transfer-border-color;
	border-radius: var.$transfer-border-radius;
	overflow: hidden;
	background: var.$color-white;
	display: inline-block;
	vertical-align: middle;
	width: var.$transfer-panel-width;
	max-height: 100%;
	box-sizing: border-box;
	position: relative;

	@include mixins.e(body) {
		height: var.$transfer-panel-body-height;

		@include mixins.when(with-footer) {
			padding-bottom: var.$transfer-panel-footer-height;
		}
	}

	@include mixins.e(list) {
		margin: 0;
		padding: 6px 0;
		list-style: none;
		height: var.$transfer-panel-body-height;
		overflow: auto;
		box-sizing: border-box;

		@include mixins.when(filterable) {
			height: #{var.$transfer-panel-body-height - var.$transfer-filter-height - 20px};
			padding-top: 0;
		}
	}

	@include mixins.e(item) {
		height: var.$transfer-item-height;
		line-height: var.$transfer-item-height;
		padding-left: 15px;
		display: block !important;

		& + .el-transfer-panel__item {
			margin-left: 0;
		}

		&.el-checkbox {
			color: var(--color-text-dark);
		}

		&:hover {
			color: var(--color-primary);
		}

		&.el-checkbox .el-checkbox__label {
			width: 100%;
			@include utils.utils-ellipsis;
			display: block;
			box-sizing: border-box;
			padding-left: 24px;
			line-height: var.$transfer-item-height;
		}

		.el-checkbox__input {
			position: absolute;
			top: 8px;
		}
	}

	@include mixins.e(filter) {
		text-align: center;
		margin: 15px;
		box-sizing: border-box;
		display: block;
		width: auto;

		.el-input__inner {
			height: var.$transfer-filter-height;
			width: 100%;
			font-size: 12px;
			display: inline-block;
			box-sizing: border-box;
			border-radius: #{var.$transfer-filter-height * 0.5};
			padding-right: 10px;
			padding-left: 30px;
		}

		.el-input__icon {
			margin-left: 5px;
		}

		.el-icon-circle-close {
			cursor: pointer;
		}
	}

	.el-transfer-panel__header {
		height: var.$transfer-panel-header-height;
		line-height: var.$transfer-panel-header-height;
		background: var.$transfer-panel-header-background-color;
		margin: 0;
		padding-left: 15px;
		border-bottom: 1px solid var.$transfer-border-color;
		box-sizing: border-box;
		color: var.$color-black;

		.el-checkbox {
			display: block;
			line-height: 40px;

			.el-checkbox__label {
				font-size: 16px;
				color: var(--color-text-dark);
				font-weight: var(--font-weight-regular);

				span {
					position: absolute;
					right: 15px;
					color: var(--color-text-light);
					font-size: 12px;
					font-weight: var(--font-weight-regular);
				}
			}
		}
	}

	.el-transfer-panel__footer {
		height: var.$transfer-panel-footer-height;
		background: var.$color-white;
		margin: 0;
		padding: 0;
		border-top: 1px solid var.$transfer-border-color;
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: var.$index-normal;
		@include utils.utils-vertical-center;

		.el-checkbox {
			padding-left: 20px;
			color: var(--color-text-dark);
		}
	}

	.el-transfer-panel__empty {
		margin: 0;
		height: var.$transfer-item-height;
		line-height: var.$transfer-item-height;
		padding: 6px 15px 0;
		color: var(--color-text-light);
		text-align: center;
	}

	.el-checkbox__label {
		padding-left: 8px;
	}

	.el-checkbox__inner {
		height: 14px;
		width: 14px;
		border-radius: 3px;
		&::after {
			height: 6px;
			width: 3px;
			left: 4px;
		}
	}
}
