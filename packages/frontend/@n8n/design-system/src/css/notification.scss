@use 'mixins/mixins';
@use './common/var';

@include mixins.b(notification) {
	display: flex;
	width: var.$notification-width;
	padding: var.$notification-padding;
	border-radius: var.$notification-radius;
	box-sizing: border-box;
	border: 1px solid var.$notification-border-color;
	position: fixed;
	background-color: var(--color-notification-background);
	box-shadow: var.$notification-shadow;
	transition:
		opacity 0.3s,
		transform 0.3s,
		left 0.3s,
		right 0.3s,
		top 0.4s,
		bottom 0.3s;
	overflow: hidden;

	&.right {
		right: 16px;
	}

	&.left {
		left: 16px;
	}

	&.content-toast {
		position: absolute;
	}

	@include mixins.e(group) {
		margin-left: var.$notification-group-margin-left;
		margin-right: var.$notification-group-margin-right;
	}

	@include mixins.e(title) {
		line-height: normal;
		font-weight: var(--font-weight-bold);
		font-size: var.$notification-title-font-size;
		color: var.$notification-title-color;
		margin: 0;

		&:first-letter {
			text-transform: uppercase;
		}
	}

	@include mixins.e(content) {
		font-size: var.$notification-content-font-size;
		line-height: 1.5;
		margin: 6px 0 0;
		color: var.$notification-content-color;
		text-align: justify;

		p {
			margin: 0;
			line-height: 1.5;
		}
	}

	@include mixins.e(icon) {
		height: var.$notification-icon-size;
		width: var.$notification-icon-size;
		font-size: var.$notification-icon-size;
	}

	@include mixins.e(closeBtn) {
		position: absolute;
		top: 18px;
		right: 15px;
		cursor: pointer;
		color: var.$notification-close-color;
		font-size: var.$notification-close-font-size;

		&:hover {
			color: var.$notification-close-hover-color;
		}
	}

	.el-icon.el-notification--success {
		color: var.$notification-success-icon-color;
	}

	.el-icon.el-notification--error {
		color: var.$notification-danger-icon-color;
	}

	.el-icon.el-notification--info {
		color: var.$notification-info-icon-color;
	}

	.el-icon.el-notification--warning {
		color: var.$notification-warning-icon-color;
	}
}

.el-notification-fade-enter-from {
	&.right {
		right: 0;
		transform: translateX(100%);
	}

	&.left {
		left: 0;
		transform: translateX(-100%);
	}
}

.el-notification-fade-leave-active {
	opacity: 0;
}
