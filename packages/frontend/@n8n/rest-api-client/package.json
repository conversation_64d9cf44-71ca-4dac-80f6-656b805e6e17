{"name": "@n8n/rest-api-client", "type": "module", "version": "1.9.0", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/*.cjs"}}, "scripts": {"dev": "vite", "build": "tsup", "preview": "vite preview", "typecheck": "vue-tsc --noEmit", "test": "vitest run", "test:dev": "vitest --silent=false", "lint": "eslint src --quiet", "lint:fix": "eslint src --fix", "format": "biome format --write . && prettier --write . --ignore-path ../../../../.prettierignore", "format:check": "biome ci . && prettier --check . --ignore-path ../../../../.prettierignore"}, "dependencies": {"@n8n/api-types": "workspace:*", "@n8n/constants": "workspace:*", "@n8n/permissions": "workspace:*", "@n8n/utils": "workspace:*", "js-base64": "catalog:", "n8n-workflow": "workspace:*", "axios": "catalog:", "flatted": "catalog:"}, "devDependencies": {"@n8n/eslint-config": "workspace:*", "@n8n/i18n": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@testing-library/jest-dom": "catalog:frontend", "@testing-library/user-event": "catalog:frontend", "tsup": "catalog:", "typescript": "catalog:", "vite": "catalog:", "vitest": "catalog:"}, "license": "See LICENSE.md file in the root of the repository"}