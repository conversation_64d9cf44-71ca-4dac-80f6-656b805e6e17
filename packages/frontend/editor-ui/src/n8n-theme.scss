@use '@n8n/design-system/css/mixins' as ds-mixins;
@use '@n8n/chat/css';
@use 'styles';

:root {
	// Using native css variable enables us to use this value in JS
	--header-height: 65;
	--content-container-width: 1280px;
}

.clickable {
	cursor: pointer !important;
}
.primary-color {
	color: var(--color-primary);
}
.text-light {
	color: var(--color-text-lighter);
	font-weight: var(--font-weight-regular);
}

// Dialog
.el-overlay {
	background-color: var(--color-dialog-overlay-background-dark);
}

#app-modals .el-overlay {
	position: absolute;
}

.el-dialog {
	border: var(--border-base);
	box-shadow: 0 6px 16px rgb(68 28 23 / 6%);
	border-radius: 8px;

	&.classic {
		.el-dialog__header {
			padding: 15px 20px;
		}

		.el-dialog__headerbtn {
			position: absolute;
			top: 0;
			right: -50px;
			color: var(--color-foreground-xlight);
			background-color: var(--color-background-dark);
			border-radius: 0 18px 18px 0;
			z-index: 110;
			font-size: 1.7em;
			text-align: center;
			line-height: 30px;
			height: 50px;
			width: 50px;
			.el-dialog__close {
				color: var(--color-foreground-xlight);
				font-weight: var(--font-weight-regular);
			}
			.el-dialog__close:hover {
				transform: scale(1.2);
			}
		}
		.el-dialog__body {
			color: var(--color-text-dark);
			padding: 0 20px 20px;
		}
		.el-dialog__title {
			color: var(--color-text-dark);
		}
	}
}

.el-message-box {
	background-color: var(--color-background-base);
	border: none;
	.el-message-box__headerbtn {
		.el-message-box__close {
			color: var(--color-text-dark);
		}
	}
	.el-message-box__content,
	.el-message-box__title {
		color: var(--color-text-dark);
	}
	.el-message-box-icon {
		width: var(--spacing-l);
		height: var(--spacing-l);
		&--warning {
			color: var(--color-warning);
		}
	}
}

// Notification Message
.el-message p {
	line-height: 1.5em;
}

// Table
.el-table {
	thead th {
		color: var(--color-text-base);
		background-color: var(--color-background-base);
	}
	tr {
		color: var(--color-text-dark);

		td {
			border: none;
		}
	}
	.tr {
		color: var(--color-text-dark);
	}
}

// Tabs
.type-selector:focus,
.el-tabs__header:focus,
.el-tabs__nav-wrap:focus,
.el-tabs__nav-scroll:focus,
.el-tabs__nav:focus {
	outline: none;
}
.el-tabs__item.is-active {
	font-weight: var(--font-weight-bold);
}

.el-tabs__content {
	border: 1px solid var(--color-foreground-base);
	border-radius: 0 var(--border-radius-base) var(--border-radius-base);
}
.el-tabs__header {
	border-bottom: 0 !important;
}
.el-tabs__nav {
	padding: 0;
	overflow: hidden;
}
.el-tabs__item {
	padding: var(--spacing-5xs) var(--spacing-2xs) !important;
	height: auto;
	line-height: var(--font-line-height-xloose);
	font-weight: var(--font-weight-regular);
	font-size: var(--font-size-2xs);

	&:not([aria-selected='true']) {
		background-color: var(--color-background-base);
		border-bottom: 1px solid var(--color-foreground-base) !important;
	}
}

// Loading Indicator
.el-loading-mask {
	background-color: var(--color-foreground-xlight);
	opacity: 0.8;
}

// Pagination
.el-pager li,
.el-pagination .btn-prev,
.el-pagination .btn-next {
	background: none;
	color: var(--color-text-dark);
}
.el-pagination button:disabled {
	background: none;
	color: var(--color-text-lighter);
}
.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
	color: var(--color-text-dark);
}

// Notification
.el-notification {
	border-radius: 4px;
	border: none;

	&.whats-new-notification {
		bottom: var(--spacing-xs) !important;
		left: var(--spacing-s) !important;
		width: 300px;
		padding: var(--spacing-xs);
		border: var(--border-base);

		.el-notification__group {
			margin-left: 0;
			margin-right: var(--spacing-l);
		}

		.el-notification__title {
			color: var(--color-callout-info-font);
			font-family: var(--font-family);
			font-size: var(--font-size-s);
			font-style: normal;
			font-weight: var(--font-weight-bold);
			line-height: 1.4;
		}

		.el-notification__content {
			color: var(--color-callout-info-font);
			font-family: var(--font-family);
			font-size: var(--font-size-s);
			font-style: normal;
			font-weight: var(--font-weight-regular);
			line-height: 1.4;
			margin-top: 0;
		}

		.el-notification__closeBtn {
			height: 100%;
			top: 0;
			right: var(--spacing-xs);
		}
	}
}

.el-notification__content {
	text-align: left;
	word-break: break-word;
	max-height: 30vh;
	overflow-y: auto;
}

.tags-container {
	.el-select-tags-wrapper .el-tag {
		font-size: 12px;
		font-weight: var(--font-weight-regular);
		display: flex;
		align-items: center;

		&.is-closable {
			overflow-y: hidden;
		}

		.el-tag__close {
			max-height: 15px;
			max-width: 15px;
			margin-left: var(--spacing-4xs);

			&:hover {
				background-color: var(--color-background-light) !important;
			}
		}
	}
}

.add-option {
	> * {
		border: none;
	}

	.el-select .el-input.is-disabled {
		.el-input__icon {
			opacity: 1 !important;
			cursor: not-allowed;
			color: var(--color-foreground-dark);
		}
		.el-input__inner,
		.el-input__inner::placeholder {
			opacity: 1;
			color: var(--color-foreground-dark);
		}
	}
	.el-select .el-input:not(.is-disabled) .el-input__icon {
		color: var(--color-text-dark);
	}
	.el-input .el-input__inner {
		text-align: center;
	}
	.el-input:not(.is-disabled) .el-input__inner {
		&,
		&:hover,
		&:focus {
			padding-left: 35px;
			border-radius: var(--border-radius-base);
			color: var(--color-text-dark);
			background-color: var(--color-background-base);
			border-color: var(--color-foreground-base);
			text-align: center;
		}

		&::placeholder {
			color: var(--color-text-dark);
			opacity: 1; /** Firefox */
		}
	}
}
