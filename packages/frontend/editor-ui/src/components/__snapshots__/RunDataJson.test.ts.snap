// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RunDataJson.vue > renders json values properly 1`] = `
<div>
  <div
    class="jsonDisplay"
  >
    <!---->
    <div
      class=""
      data-test-id="draggable"
    >
      
      <div
        class="vjs-tree is-virtual json-data"
      >
        <div
          class="vjs-tree-list"
          style="height: 500px;"
        >
          <div
            class="vjs-tree-list-holder"
            style="height: 340px;"
          >
            <div
              class="vjs-tree-list-holder-inner"
              style="transform: translateY(0px);"
            >
              
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    [
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    {
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="list"
                    data-path="[0].list"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.list }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "list"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    [
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-value vjs-value-number"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="3"
                      data-name="list[0]"
                      data-path="[0].list[0]"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.list[0] }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        1
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-value vjs-value-number"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="3"
                      data-name="list[1]"
                      data-path="[0].list[1]"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.list[1] }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        2
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-value vjs-value-number"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="3"
                      data-name="list[2]"
                      data-path="[0].list[2]"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.list[2] }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        3
                      </span>
                      
                    </span>
                    
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    ]
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="record"
                    data-path="[0].record"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.record }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "record"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    {
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="3"
                    data-name="name"
                    data-path="[0].record.name"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.record.name }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "name"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-string"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="3"
                      data-name="record.name"
                      data-path="[0].record.name"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.record.name }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        "Joe"
                      </span>
                      
                    </span>
                    
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    }
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="myNumber"
                    data-path="[0].myNumber"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.myNumber }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "myNumber"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-number"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="2"
                      data-name="myNumber"
                      data-path="[0].myNumber"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.myNumber }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        123
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="myStringNumber"
                    data-path="[0].myStringNumber"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.myStringNumber }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "myStringNumber"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-string"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="2"
                      data-name="myStringNumber"
                      data-path="[0].myStringNumber"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.myStringNumber }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        "456"
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="myStringText"
                    data-path="[0].myStringText"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.myStringText }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "myStringText"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-string"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="2"
                      data-name="myStringText"
                      data-path="[0].myStringText"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.myStringText }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        "abc"
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="nil"
                    data-path="[0].nil"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.nil }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "nil"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-null"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="2"
                      data-name="nil"
                      data-path="[0].nil"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.nil }}"
                    >
                      
                      <span>
                        <!--v-if-->
                        null
                      </span>
                      
                    </span>
                    
                  </span>
                  <span>
                    ,
                  </span>
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <span
                  class="vjs-key"
                >
                  
                  <span
                    class="content mappable"
                    data-depth="2"
                    data-name="d"
                    data-path="[0].d"
                    data-target="mappable"
                    data-value="{{ $('Set').item.json.d }}"
                  >
                    
                    <span>
                      <!--v-if-->
                      "d"
                    </span>
                    
                  </span>
                  
                  <span
                    class="vjs-colon"
                  >
                    : 
                  </span>
                </span>
                <span>
                  <span
                    class="vjs-value vjs-value-undefined"
                  >
                    
                    <span
                      class="content mappable ph-no-capture"
                      data-depth="2"
                      data-name="d"
                      data-path="[0].d"
                      data-target="mappable"
                      data-value="{{ $('Set').item.json.d }}"
                    >
                      <span />
                    </span>
                    
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  <div
                    class="vjs-indent-unit has-line"
                  />
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    }
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              <div
                class="vjs-tree-node"
              >
                <!---->
                <!---->
                <div
                  class="vjs-indent"
                >
                  
                  
                  <!---->
                </div>
                <!---->
                <span>
                  <span
                    class="vjs-tree-brackets"
                  >
                    ]
                  </span>
                  <!---->
                  <!---->
                </span>
              </div>
              
            </div>
          </div>
        </div>
      </div>
      
      <!--teleport start-->
      <!--teleport end-->
    </div>
  </div>
</div>
`;
