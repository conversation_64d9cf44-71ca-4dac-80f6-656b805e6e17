// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CanvasNodeDefault > configurable > should render configurable node correctly 1`] = `
<div
  class="node configurable"
  data-test-id="canvas-configurable-node"
  style="--canvas-node--width: 224px; --canvas-node--height: 96px; --node-icon-size: 40px;"
>
  <!--v-if-->
  <div
    class="n8n-node-icon nodeIcon icon nodeIcon icon"
    shrink="false"
  >
    <div
      class="nodeIconWrapper"
      style="width: 40px; height: 40px; font-size: 40px; line-height: 40px;"
    >
      <!-- ElementUI tooltip is prone to memory-leaking so we only render it if we really need it -->
      
      <div
        class="nodeIconPlaceholder"
      >
        ?
      </div>
      
    </div>
  </div>
  <div
    class="settingsIcons"
  >
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <!--v-if-->
  <div
    class="description"
  >
    <div
      class="label"
    >
      Test Node
    </div>
    <!--v-if-->
    <div
      class="subtitle"
    >
      Test Node Subtitle
    </div>
  </div>
  <!--v-if-->
</div>
`;

exports[`CanvasNodeDefault > configuration > should render configurable configuration node correctly 1`] = `
<div
  class="node configurable configuration"
  data-test-id="canvas-configurable-node"
  style="--canvas-node--width: 240px; --canvas-node--height: 80px; --node-icon-size: 30px;"
>
  <!--v-if-->
  <div
    class="n8n-node-icon nodeIcon icon nodeIcon icon"
    shrink="false"
  >
    <div
      class="nodeIconWrapper"
      style="width: 30px; height: 30px; font-size: 30px; line-height: 30px;"
    >
      <!-- ElementUI tooltip is prone to memory-leaking so we only render it if we really need it -->
      
      <div
        class="nodeIconPlaceholder"
      >
        ?
      </div>
      
    </div>
  </div>
  <!--v-if-->
  <!--v-if-->
  <div
    class="description"
  >
    <div
      class="label"
    >
      Test Node
    </div>
    <!--v-if-->
    <div
      class="subtitle"
    >
      Test Node Subtitle
    </div>
  </div>
  <!--v-if-->
</div>
`;

exports[`CanvasNodeDefault > configuration > should render configuration node correctly 1`] = `
<div
  class="node configuration"
  data-test-id="canvas-configuration-node"
  style="--canvas-node--width: 80px; --canvas-node--height: 80px; --node-icon-size: 30px;"
>
  <!--v-if-->
  <div
    class="n8n-node-icon nodeIcon icon nodeIcon icon"
    shrink="false"
  >
    <div
      class="nodeIconWrapper"
      style="width: 30px; height: 30px; font-size: 30px; line-height: 30px;"
    >
      <!-- ElementUI tooltip is prone to memory-leaking so we only render it if we really need it -->
      
      <div
        class="nodeIconPlaceholder"
      >
        ?
      </div>
      
    </div>
  </div>
  <!--v-if-->
  <!--v-if-->
  <div
    class="description"
  >
    <div
      class="label"
    >
      Test Node
    </div>
    <!--v-if-->
    <div
      class="subtitle"
    >
      Test Node Subtitle
    </div>
  </div>
  <!--v-if-->
</div>
`;

exports[`CanvasNodeDefault > should render node correctly 1`] = `
<div
  class="node"
  data-test-id="canvas-default-node"
  style="--canvas-node--width: 96px; --canvas-node--height: 96px; --node-icon-size: 40px;"
>
  <!--v-if-->
  <div
    class="n8n-node-icon nodeIcon icon nodeIcon icon"
    shrink="false"
  >
    <div
      class="nodeIconWrapper"
      style="width: 40px; height: 40px; font-size: 40px; line-height: 40px;"
    >
      <!-- ElementUI tooltip is prone to memory-leaking so we only render it if we really need it -->
      
      <div
        class="nodeIconPlaceholder"
      >
        ?
      </div>
      
    </div>
  </div>
  <div
    class="settingsIcons"
  >
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <!--v-if-->
  <div
    class="description"
  >
    <div
      class="label"
    >
      Test Node
    </div>
    <!--v-if-->
    <div
      class="subtitle"
    >
      Test Node Subtitle
    </div>
  </div>
  <!--v-if-->
</div>
`;

exports[`CanvasNodeDefault > trigger > should render trigger node correctly 1`] = `
<div
  class="node trigger"
  data-test-id="canvas-trigger-node"
  style="--canvas-node--width: 96px; --canvas-node--height: 96px; --node-icon-size: 40px;"
>
  <!--v-if-->
  <div
    class="n8n-node-icon nodeIcon icon nodeIcon icon"
    shrink="false"
  >
    <div
      class="nodeIconWrapper"
      style="width: 40px; height: 40px; font-size: 40px; line-height: 40px;"
    >
      <!-- ElementUI tooltip is prone to memory-leaking so we only render it if we really need it -->
      
      <div
        class="nodeIconPlaceholder"
      >
        ?
      </div>
      
    </div>
  </div>
  <div
    class="settingsIcons"
  >
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
  </div>
  <!--v-if-->
  <div
    class="description"
  >
    <div
      class="label"
    >
      Test Node
    </div>
    <!--v-if-->
    <div
      class="subtitle"
    >
      Test Node Subtitle
    </div>
  </div>
  <!--v-if-->
</div>
`;
