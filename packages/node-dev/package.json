{"name": "n8n-node-dev", "version": "1.104.0", "description": "CLI to simplify n8n credentials/node development", "main": "dist/src/index", "types": "dist/src/index.d.ts", "oclif": {"commands": "./dist/commands", "bin": "n8n-node-dev"}, "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "build": "tsc --noEmit", "build-node-dev": "tsc", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint src --quiet", "lint:fix": "eslint src --fix", "prepack": "echo \"Building project...\" && rm -rf dist && tsc -b", "watch": "tsc --watch"}, "bin": {"n8n-node-dev": "./bin/n8n-node-dev"}, "keywords": ["development", "node", "helper", "n8n"], "files": ["bin", "dist", "templates", "src/tsconfig-build.json"], "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/inquirer": "^6.5.0"}, "dependencies": {"@n8n/di": "workspace:*", "@oclif/core": "4.0.7", "change-case": "^4.1.1", "fast-glob": "catalog:", "inquirer": "^7.0.1", "n8n-core": "workspace:*", "n8n-workflow": "workspace:*", "replace-in-file": "^6.0.0", "tmp-promise": "^3.0.3"}}