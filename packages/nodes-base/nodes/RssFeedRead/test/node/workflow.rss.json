{"name": "rss feed test", "nodes": [{"parameters": {}, "id": "448372da-bc2d-4952-8d16-4b3384cd3c3d", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-112, -176]}, {"parameters": {"url": "https://lorem-rss.herokuapp.com/feed?length=3", "options": {}}, "id": "13b7f0a0-db25-4ff3-9e61-4748f6910860", "name": "RSS Read", "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1, "position": [96, -176]}, {"parameters": {"url": "https://fake-rss-feed.com/feed", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.2, "position": [96, 16], "id": "ec469874-0ca2-457e-9204-77417d08ef90", "name": "RSS Read1"}, {"parameters": {"url": "https://custom-rss-feed.com/feed", "options": {"customFields": "custom"}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.2, "position": [96, 176], "id": "6b9593c7-22e0-4ed6-a2ca-bd48aecdfc59", "name": "RSS With Custom"}], "pinData": {"RSS Read": [{"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:40:00Z", "link": "http://example.com/test/1675950000", "pubDate": "Thu, 09 Feb 2023 13:40:00 GMT", "dc:creator": "<PERSON>", "content": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "contentSnippet": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "guid": "http://example.com/test/1675950000", "isoDate": "2023-02-09T13:40:00.000Z"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:39:00Z", "link": "http://example.com/test/1675949940", "pubDate": "Thu, 09 Feb 2023 13:39:00 GMT", "dc:creator": "<PERSON>", "content": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "contentSnippet": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "guid": "http://example.com/test/1675949940", "isoDate": "2023-02-09T13:39:00.000Z"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:38:00Z", "link": "http://example.com/test/1675949880", "pubDate": "Thu, 09 Feb 2023 13:38:00 GMT", "dc:creator": "<PERSON>", "content": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "contentSnippet": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "guid": "http://example.com/test/1675949880", "isoDate": "2023-02-09T13:38:00.000Z"}}], "RSS Read1": [{"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:40:00Z", "link": "http://example.com/test/1675950000", "pubDate": "Thu, 09 Feb 2023 13:40:00 GMT", "dc:creator": "<PERSON>", "content": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "contentSnippet": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "guid": "http://example.com/test/1675950000", "isoDate": "2023-02-09T13:40:00.000Z"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:39:00Z", "link": "http://example.com/test/1675949940", "pubDate": "Thu, 09 Feb 2023 13:39:00 GMT", "dc:creator": "<PERSON>", "content": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "contentSnippet": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "guid": "http://example.com/test/1675949940", "isoDate": "2023-02-09T13:39:00.000Z"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:38:00Z", "link": "http://example.com/test/1675949880", "pubDate": "Thu, 09 Feb 2023 13:38:00 GMT", "dc:creator": "<PERSON>", "content": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "contentSnippet": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "guid": "http://example.com/test/1675949880", "isoDate": "2023-02-09T13:38:00.000Z"}}], "RSS With Custom": [{"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:40:00Z", "link": "http://example.com/test/1675950000", "pubDate": "Thu, 09 Feb 2023 13:40:00 GMT", "dc:creator": "<PERSON>", "content": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "contentSnippet": "Fugiat excepteur exercitation tempor ut aute sunt pariatur veniam pariatur dolor.", "guid": "http://example.com/test/1675950000", "isoDate": "2023-02-09T13:40:00.000Z", "custom": "custom"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:39:00Z", "link": "http://example.com/test/1675949940", "pubDate": "Thu, 09 Feb 2023 13:39:00 GMT", "dc:creator": "<PERSON>", "content": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "contentSnippet": "Laboris quis nulla tempor eu ullamco est esse qui aute commodo aliqua occaecat.", "guid": "http://example.com/test/1675949940", "isoDate": "2023-02-09T13:39:00.000Z", "custom": "custom"}}, {"json": {"creator": "<PERSON>", "title": "Lorem ipsum 2023-02-09T13:38:00Z", "link": "http://example.com/test/1675949880", "pubDate": "Thu, 09 Feb 2023 13:38:00 GMT", "dc:creator": "<PERSON>", "content": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "contentSnippet": "<PERSON><PERSON>re labore dolor dolore sint aliquip eu anim aute anim et nulla adipisicing nostrud.", "guid": "http://example.com/test/1675949880", "isoDate": "2023-02-09T13:38:00.000Z", "custom": "custom"}}]}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "RSS Read", "type": "main", "index": 0}, {"node": "RSS Read1", "type": "main", "index": 0}, {"node": "RSS With Custom", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "11023520-13ee-48bf-80b4-81821316ec5f", "meta": {"instanceId": "0fa937d34dcabeff4bd6480d3b42cc95edf3bc20e6810819086ef1ce2623639d"}, "id": "hKwPhUVv6w8I1s7M", "tags": []}