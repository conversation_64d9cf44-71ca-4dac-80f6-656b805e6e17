{"name": "My workflow 12", "nodes": [{"parameters": {}, "id": "7db7d51a-83c2-4aa0-a736-9c3d1c031b60", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [360, 340]}, {"parameters": {"authentication": "serviceAccount", "projectId": {"__rl": true, "value": "test-project", "mode": "list", "cachedResultName": "test-project", "cachedResultUrl": "https://console.cloud.google.com/bigquery?project=test-project"}, "sqlQuery": "SELECT * FROM bigquery_node_dev_test_dataset.test_json WHERE email = @email AND name = @name AND n8n_variable = @n8n_variable;", "options": {"queryParameters": {"namedParameters": [{"name": "email", "value": "<EMAIL>"}, {"name": "name", "value": "Test Testerson"}, {"name": "n8n_variable", "value": "={{ 40 + 2 }}"}]}}}, "id": "83d00275-0f98-4d5e-a3d6-bbca940ff8ac", "name": "Google BigQuery", "type": "n8n-nodes-base.googleBigQuery", "typeVersion": 2, "position": [620, 340], "credentials": {"googleApi": {"id": "66", "name": "Google account 5"}}}], "pinData": {"Google BigQuery": []}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Google BigQuery", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "be2fc126-5d71-4e86-9a4e-eb62ad266860", "id": "156", "meta": {"instanceId": "36203ea1ce3cef713fa25999bd9874ae26b9e4c2c3a90a365f2882a154d031d0"}, "tags": []}